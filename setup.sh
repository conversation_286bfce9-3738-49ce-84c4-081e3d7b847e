#!/bin/bash

# Create React app
npx create-react-app video-conference-iframe
cd video-conference-iframe

# Remove default files we don't need
rm src/App.css src/App.test.js src/index.css src/logo.svg src/reportWebVitals.js src/setupTests.js

# Create the VideoConference component
cat > src/VideoConference.js << 'EOF'
import React, { useState } from 'react';

export default function VideoConference() {
  const [meetingUrl, setMeetingUrl] = useState('');

  return (
    <div style={{ padding: '20px', height: '100vh' }}>
      <div style={{ marginBottom: '10px' }}>
        <input
          type="url"
          placeholder="Paste your meeting link here..."
          value={meetingUrl}
          onChange={(e) => setMeetingUrl(e.target.value)}
          style={{
            width: '400px',
            padding: '10px',
            marginRight: '10px',
            border: '1px solid #ccc',
            borderRadius: '4px'
          }}
        />
        <button
          onClick={() => setMeetingUrl('')}
          style={{
            padding: '10px 20px',
            backgroundColor: '#f44336',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Clear
        </button>
      </div>
      
      {meetingUrl && (
        <iframe
          src={meetingUrl}
          width="100%"
          height="90%"
          frameBorder="0"
          allow="camera; microphone; fullscreen; speaker; display-capture; autoplay; encrypted-media; gyroscope; accelerometer; clipboard-read; clipboard-write; web-share"
          allowFullScreen
          sandbox="allow-same-origin allow-scripts allow-popups allow-forms allow-modals allow-orientation-lock allow-pointer-lock allow-presentation allow-top-navigation allow-top-navigation-by-user-activation"
          style={{
            border: '1px solid #ddd',
            borderRadius: '8px'
          }}
        />
      )}
    </div>
  );
}
EOF

# Update App.js
cat > src/App.js << 'EOF'
import React from 'react';
import VideoConference from './VideoConference';

function App() {
  return (
    <div className="App">
      <VideoConference />
    </div>
  );
}

export default App;
EOF

# Update index.js
cat > src/index.js << 'EOF'
import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(<App />);
EOF

# Update public/index.html
cat > public/index.html << 'EOF'
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="Video Conference Iframe App" />
    <title>Video Conference</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>
EOF

echo "Project created successfully!"
echo "To run the project:"
echo "1. cd video-conference-iframe"
echo "2. npm start"