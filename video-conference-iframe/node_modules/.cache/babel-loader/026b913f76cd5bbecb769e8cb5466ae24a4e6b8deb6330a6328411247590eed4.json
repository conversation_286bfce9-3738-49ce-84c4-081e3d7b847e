{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/iframeNew2/video-conference-iframe/src/VideoConference.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function VideoConference() {\n  _s();\n  const [meetingUrl, setMeetingUrl] = useState('');\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '20px',\n      height: '100vh'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '10px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"url\",\n        placeholder: \"Paste your meeting link here...\",\n        value: meetingUrl,\n        onChange: e => setMeetingUrl(e.target.value),\n        style: {\n          width: '400px',\n          padding: '10px',\n          marginRight: '10px',\n          border: '1px solid #ccc',\n          borderRadius: '4px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setMeetingUrl(''),\n        style: {\n          padding: '10px 20px',\n          backgroundColor: '#f44336',\n          color: 'white',\n          border: 'none',\n          borderRadius: '4px',\n          cursor: 'pointer'\n        },\n        children: \"Clear\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this), meetingUrl && /*#__PURE__*/_jsxDEV(\"iframe\", {\n      src: meetingUrl,\n      width: \"100%\",\n      height: \"90%\",\n      frameBorder: \"0\",\n      allow: \"camera; microphone; fullscreen; display-capture; autoplay; encrypted-media; gyroscope; accelerometer; clipboard-read; clipboard-write; web-share\",\n      sandbox: \"allow-same-origin allow-scripts allow-popups allow-forms allow-modals allow-orientation-lock allow-pointer-lock allow-presentation allow-top-navigation allow-top-navigation-by-user-activation\",\n      style: {\n        border: '1px solid #ddd',\n        borderRadius: '8px'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n}\n_s(VideoConference, \"4EnjucUff8Zy3Q7BRVuK051JgEs=\");\n_c = VideoConference;\nvar _c;\n$RefreshReg$(_c, \"VideoConference\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "VideoConference", "_s", "meetingUrl", "setMeetingUrl", "style", "padding", "height", "children", "marginBottom", "type", "placeholder", "value", "onChange", "e", "target", "width", "marginRight", "border", "borderRadius", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "backgroundColor", "color", "cursor", "src", "frameBorder", "allow", "sandbox", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/iframeNew2/video-conference-iframe/src/VideoConference.js"], "sourcesContent": ["import React, { useState } from 'react';\n\nexport default function VideoConference() {\n  const [meetingUrl, setMeetingUrl] = useState('');\n\n  return (\n    <div style={{ padding: '20px', height: '100vh' }}>\n      <div style={{ marginBottom: '10px' }}>\n        <input\n          type=\"url\"\n          placeholder=\"Paste your meeting link here...\"\n          value={meetingUrl}\n          onChange={(e) => setMeetingUrl(e.target.value)}\n          style={{\n            width: '400px',\n            padding: '10px',\n            marginRight: '10px',\n            border: '1px solid #ccc',\n            borderRadius: '4px'\n          }}\n        />\n        <button\n          onClick={() => setMeetingUrl('')}\n          style={{\n            padding: '10px 20px',\n            backgroundColor: '#f44336',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px',\n            cursor: 'pointer'\n          }}\n        >\n          Clear\n        </button>\n      </div>\n      \n      {meetingUrl && (\n        <iframe\n          src={meetingUrl}\n          width=\"100%\"\n          height=\"90%\"\n          frameBorder=\"0\"\n          allow=\"camera; microphone; fullscreen; display-capture; autoplay; encrypted-media; gyroscope; accelerometer; clipboard-read; clipboard-write; web-share\"\n          sandbox=\"allow-same-origin allow-scripts allow-popups allow-forms allow-modals allow-orientation-lock allow-pointer-lock allow-presentation allow-top-navigation allow-top-navigation-by-user-activation\"\n          style={{\n            border: '1px solid #ddd',\n            borderRadius: '8px'\n          }}\n        />\n      )}\n    </div>\n  );\n}"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,eAAe,SAASC,eAAeA,CAAA,EAAG;EAAAC,EAAA;EACxC,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGN,QAAQ,CAAC,EAAE,CAAC;EAEhD,oBACEE,OAAA;IAAKK,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,MAAM,EAAE;IAAQ,CAAE;IAAAC,QAAA,gBAC/CR,OAAA;MAAKK,KAAK,EAAE;QAAEI,YAAY,EAAE;MAAO,CAAE;MAAAD,QAAA,gBACnCR,OAAA;QACEU,IAAI,EAAC,KAAK;QACVC,WAAW,EAAC,iCAAiC;QAC7CC,KAAK,EAAET,UAAW;QAClBU,QAAQ,EAAGC,CAAC,IAAKV,aAAa,CAACU,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;QAC/CP,KAAK,EAAE;UACLW,KAAK,EAAE,OAAO;UACdV,OAAO,EAAE,MAAM;UACfW,WAAW,EAAE,MAAM;UACnBC,MAAM,EAAE,gBAAgB;UACxBC,YAAY,EAAE;QAChB;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACFvB,OAAA;QACEwB,OAAO,EAAEA,CAAA,KAAMpB,aAAa,CAAC,EAAE,CAAE;QACjCC,KAAK,EAAE;UACLC,OAAO,EAAE,WAAW;UACpBmB,eAAe,EAAE,SAAS;UAC1BC,KAAK,EAAE,OAAO;UACdR,MAAM,EAAE,MAAM;UACdC,YAAY,EAAE,KAAK;UACnBQ,MAAM,EAAE;QACV,CAAE;QAAAnB,QAAA,EACH;MAED;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAELpB,UAAU,iBACTH,OAAA;MACE4B,GAAG,EAAEzB,UAAW;MAChBa,KAAK,EAAC,MAAM;MACZT,MAAM,EAAC,KAAK;MACZsB,WAAW,EAAC,GAAG;MACfC,KAAK,EAAC,kJAAkJ;MACxJC,OAAO,EAAC,iMAAiM;MACzM1B,KAAK,EAAE;QACLa,MAAM,EAAE,gBAAgB;QACxBC,YAAY,EAAE;MAChB;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACrB,EAAA,CAlDuBD,eAAe;AAAA+B,EAAA,GAAf/B,eAAe;AAAA,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}