import React, { useState } from 'react';

export default function VideoConference() {
  const [meetingUrl, setMeetingUrl] = useState('');

  return (
    <div style={{ padding: '20px', height: '100vh' }}>
      <div style={{ marginBottom: '10px' }}>
        <input
          type="url"
          placeholder="Paste your meeting link here..."
          value={meetingUrl}
          onChange={(e) => setMeetingUrl(e.target.value)}
          style={{
            width: '400px',
            padding: '10px',
            marginRight: '10px',
            border: '1px solid #ccc',
            borderRadius: '4px'
          }}
        />
        <button
          onClick={() => setMeetingUrl('')}
          style={{
            padding: '10px 20px',
            backgroundColor: '#f44336',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Clear
        </button>
      </div>
      
      {meetingUrl && (
        <iframe
          src={meetingUrl}
          width="100%"
          height="90%"
          frameBorder="0"
          allow="camera; microphone; fullscreen; display-capture; autoplay; encrypted-media; gyroscope; accelerometer; clipboard-read; clipboard-write; web-share"
          sandbox="allow-same-origin allow-scripts allow-popups allow-forms allow-modals allow-orientation-lock allow-pointer-lock allow-presentation allow-top-navigation allow-top-navigation-by-user-activation"
          style={{
            border: '1px solid #ddd',
            borderRadius: '8px'
          }}
        />
      )}
    </div>
  );
}